# دليل نظام الصلاحيات - إخفاء الصفحات والمكونات

## نظرة عامة

تم تطبيق نظام شامل لإخفاء صفحات لوحة التحكم والمكونات بناءً على صلاحيات المستخدم. النظام يعمل على مستويين:

1. **إخفاء عناصر القائمة**: عناصر القائمة الجانبية تختفي تلقائياً
2. **حماية الصفحات والمكونات**: حماية المحتوى داخل الصفحات

## الملفات المضافة/المحدثة

### 1. Hook الصلاحيات الجديد
- **الملف**: `hooks/usePermission.ts`
- **الوظيفة**: Hook مركزي للتحقق من صلاحيات المستخدم

### 2. مكونات الحماية
- **الملف**: `components/PermissionGuard.tsx`
- **الوظيفة**: مكونات لحماية المحتوى بناءً على الصلاحيات

### 3. التنقل المحدث
- **الملف**: `components/main-nav.tsx`
- **التحديث**: استخدام النظام الجديد لإخفاء عناصر القائمة

### 4. صفحة تجريبية
- **الملف**: `app/(main)/demo-permissions/page.tsx`
- **الوظيفة**: توضيح كيفية عمل النظام

## كيفية الاستخدام

### 1. إخفاء عناصر القائمة (تلقائي)

عناصر القائمة الجانبية تختفي تلقائياً بناءً على صلاحية `view` للمستخدم الحالي.

```tsx
// في components/main-nav.tsx
function NavItem({ link, pathname }: { link: typeof links[0]; pathname: string }) {
  const canView = useCanView(link.permissionKey);
  
  // إخفاء العنصر إذا لم تكن لدى المستخدم صلاحية العرض
  if (!canView) return null;

  return (
    <SidebarMenuItem>
      {/* محتوى العنصر */}
    </SidebarMenuItem>
  );
}
```

### 2. حماية الصفحات الكاملة

```tsx
import { PermissionGuard } from '@/components/PermissionGuard';

export default function MyPage() {
  return (
    <PermissionGuard pageKey="supply">
      <div>
        {/* محتوى الصفحة */}
      </div>
    </PermissionGuard>
  );
}
```

### 3. حماية الأزرار والإجراءات

```tsx
import { ActionGuard } from '@/components/PermissionGuard';

function MyComponent() {
  return (
    <div>
      <ActionGuard pageKey="supply" action="create">
        <Button>إنشاء أمر توريد</Button>
      </ActionGuard>
      
      <ActionGuard pageKey="supply" action="edit">
        <Button>تعديل</Button>
      </ActionGuard>
      
      <ActionGuard pageKey="supply" action="delete">
        <Button variant="destructive">حذف</Button>
      </ActionGuard>
    </div>
  );
}
```

### 4. إخفاء أقسام معينة

```tsx
import { ViewGuard } from '@/components/PermissionGuard';

function MyComponent() {
  return (
    <div>
      <ViewGuard pageKey="maintenance">
        <div>
          {/* هذا القسم يظهر فقط لمن لديه صلاحية عرض الصيانة */}
        </div>
      </ViewGuard>
      
      <ViewGuard 
        pageKey="sales" 
        fallback={<p>ليس لديك صلاحية لعرض هذا القسم</p>}
      >
        <div>
          {/* محتوى المبيعات */}
        </div>
      </ViewGuard>
    </div>
  );
}
```

### 5. استخدام Hook الصلاحيات مباشرة

```tsx
import { usePermission, useCanView } from '@/hooks/usePermission';

function MyComponent() {
  const supplyPermissions = usePermission('supply');
  const canViewSales = useCanView('sales');

  return (
    <div>
      {canViewSales && <p>يمكنك عرض المبيعات</p>}
      
      {supplyPermissions.canCreate && (
        <Button>إنشاء أمر توريد</Button>
      )}
      
      {supplyPermissions.canDelete && (
        <Button variant="destructive">حذف</Button>
      )}
    </div>
  );
}
```

## أنواع الصلاحيات المدعومة

- **view**: صلاحية العرض
- **create**: صلاحية الإنشاء
- **edit**: صلاحية التعديل
- **delete**: صلاحية الحذف
- **viewAll**: صلاحية عرض جميع البيانات (للمراسلات)
- **manage**: صلاحية إدارة مخازن معينة
- **acceptWithoutWarranty**: صلاحية قبول المرتجعات بدون ضمان

## مفاتيح الصفحات المدعومة

```typescript
type PermissionPageKey = 
  | 'dashboard'
  | 'track'
  | 'supply'
  | 'acceptDevices'
  | 'grading'
  | 'inventory'
  | 'sales'
  | 'maintenance'
  | 'maintenanceTransfer'
  | 'warehouseTransfer'
  | 'clients'
  | 'pricing'
  | 'returns'
  | 'warehouses'
  | 'users'
  | 'reports'
  | 'stocktaking'
  | 'settings'
  | 'requests'
  | 'messaging';
```

## تجربة النظام

1. قم بتشغيل التطبيق: `npm run dev`
2. انتقل إلى `/demo-permissions` لرؤية النظام في العمل
3. جرب تغيير المستخدم الحالي في المتجر لرؤية الاختلافات

## المستخدمون الافتراضيون

- **مدير النظام** (admin): صلاحيات كاملة لجميع الصفحات
- **موظف مبيعات** (sales): صلاحيات محدودة للمبيعات والمخزون
- **فني صيانة** (maint): بدون صلاحيات (للاختبار)
- **أمين مخزن** (wh): بدون صلاحيات (للاختبار)

## ملاحظات مهمة

1. **الأمان**: إخفاء المكونات لا يكفي للأمان الكامل، يجب تطبيق الحماية على مستوى الخادم أيضاً
2. **الأداء**: النظام محسن لتجنب إعادة الحساب غير الضرورية
3. **التوافق**: النظام متوافق مع البنية الحالية للتطبيق
4. **التوسع**: يمكن إضافة صلاحيات جديدة بسهولة

## التطوير المستقبلي

- إضافة نظام أدوار أكثر تعقيداً
- تطبيق الحماية على مستوى الخادم
- إضافة صلاحيات على مستوى البيانات الفردية
- تسجيل محاولات الوصول غير المصرح بها
