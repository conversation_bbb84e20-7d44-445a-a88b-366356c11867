'use client';

import { useStore } from '@/context/store';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { User } from 'lucide-react';

/**
 * مكون لتبديل المستخدم الحالي (للاختبار فقط)
 */
export function UserSwitcher() {
  const { users, currentUser, setCurrentUser } = useStore();

  const handleUserChange = (userId: string) => {
    const selectedUser = users.find(user => user.id.toString() === userId);
    if (selectedUser) {
      setCurrentUser(selectedUser);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          تبديل المستخدم
        </CardTitle>
        <CardDescription>
          اختر مستخدماً مختلفاً لاختبار نظام الصلاحيات
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Select
          value={currentUser?.id.toString() || ''}
          onValueChange={handleUserChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="اختر مستخدماً" />
          </SelectTrigger>
          <SelectContent>
            {users.map((user) => (
              <SelectItem key={user.id} value={user.id.toString()}>
                <div className="flex flex-col items-start">
                  <span className="font-medium">{user.name}</span>
                  <span className="text-sm text-muted-foreground">
                    @{user.username}
                  </span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        {currentUser && (
          <div className="mt-4 p-3 bg-muted rounded-lg">
            <p className="text-sm font-medium">المستخدم الحالي:</p>
            <p className="text-lg">{currentUser.name}</p>
            <p className="text-sm text-muted-foreground">@{currentUser.username}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
