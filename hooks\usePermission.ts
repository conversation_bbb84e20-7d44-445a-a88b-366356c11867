import { useStore } from '@/context/store';
import { PermissionPageKey } from '@/lib/types';

/**
 * Hook مركزي لاختبار صلاحيات المستخدم
 * يتحقق من صلاحيات المستخدم الحالي للوصول إلى صفحات أو وحدات معينة
 */
export function usePermission(pageKey: PermissionPageKey) {
  const { currentUser } = useStore();

  // إذا لم يكن هناك مستخدم حالي، لا يُسمح بالوصول
  if (!currentUser) {
    return {
      canView: false,
      canCreate: false,
      canEdit: false,
      canDelete: false,
      canViewAll: false,
      canManage: [],
      canAcceptWithoutWarranty: false,
    };
  }

  // الحصول على صلاحيات الصفحة المحددة
  const pagePermissions = currentUser.permissions[pageKey];

  if (!pagePermissions) {
    return {
      canView: false,
      canCreate: false,
      canEdit: false,
      canDelete: false,
      canViewAll: false,
      canManage: [],
      canAcceptWithoutWarranty: false,
    };
  }

  return {
    canView: pagePermissions.view || false,
    canCreate: pagePermissions.create || false,
    canEdit: pagePermissions.edit || false,
    canDelete: pagePermissions.delete || false,
    canViewAll: pagePermissions.viewAll || false,
    canManage: pagePermissions.manage || [],
    canAcceptWithoutWarranty: pagePermissions.acceptWithoutWarranty || false,
  };
}

/**
 * Hook مبسط للتحقق من صلاحية العرض فقط
 */
export function useCanView(pageKey: PermissionPageKey): boolean {
  const { canView } = usePermission(pageKey);
  return canView;
}

/**
 * Hook للتحقق من صلاحيات متعددة
 */
export function useMultiplePermissions(pageKeys: PermissionPageKey[]) {
  const { currentUser } = useStore();

  if (!currentUser) {
    return pageKeys.reduce((acc, key) => {
      acc[key] = {
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false,
      };
      return acc;
    }, {} as Record<PermissionPageKey, any>);
  }

  return pageKeys.reduce((acc, key) => {
    const pagePermissions = currentUser.permissions[key];
    acc[key] = {
      canView: pagePermissions?.view || false,
      canCreate: pagePermissions?.create || false,
      canEdit: pagePermissions?.edit || false,
      canDelete: pagePermissions?.delete || false,
    };
    return acc;
  }, {} as Record<PermissionPageKey, any>);
}
