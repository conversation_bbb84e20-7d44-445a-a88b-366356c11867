'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import {
  Device,
  Contact,
  Warehouse,
  Sale,
  SaleItem,
  Return,
  DeviceStatus,
  Manufacturer,
  DeviceModel,
  SupplyOrder,
  EvaluationOrder,
  MaintenanceLog,
  MaintenanceResult,
  WarehouseTransfer,
  User,
  SystemSettings,
  EmployeeRequest,
  InternalMessage,
  MessageStatus,
  AcceptanceOrder,
  MaintenanceOrder,
  DeliveryOrder,
  DeliveryOrderItem,
  MaintenanceReceiptOrder,
  DeviceReturnHistory,
  Stocktake,
  StocktakeDiscrepancy,
  StocktakeStatus,
  StocktakeFilter,
  StocktakeV1, // Import StocktakeV1
  StocktakeItemV1, // Import StocktakeItemV1
} from '@/lib/types';
import {
  initialDevices,
  initialClients,
  initialSuppliers,
  initialWarehouses,
  initialSales,
  initialReturns,
  initialManufacturers,
  initialDeviceModels,
  initialSupplyOrders,
  initialEvaluationOrders,
  initialWarehouseTransfers,
  initialUsers,
  initialSystemSettings,
  initialEmployeeRequests,
  initialInternalMessages,
  initialAcceptanceOrders,
  initialMaintenanceOrders,
  initialDeliveryOrders,
} from '@/lib/data';
import type { ActivityLog } from '@/lib/types';

type StoreContextType = {
  devices: Device[];
  clients: Contact[];
  suppliers: Contact[];
  warehouses: Warehouse[];
  sales: Sale[];
  returns: Return[];
  activities: ActivityLog[];
  manufacturers: Manufacturer[];
  deviceModels: DeviceModel[];
  supplyOrders: SupplyOrder[];
  evaluationOrders: EvaluationOrder[];
  maintenanceHistory: MaintenanceLog[];
  warehouseTransfers: WarehouseTransfer[];
  users: User[];
  currentUser: User | null;
  systemSettings: SystemSettings;
  employeeRequests: EmployeeRequest[];
  internalMessages: InternalMessage[];
  acceptanceOrders: AcceptanceOrder[];
  maintenanceOrders: MaintenanceOrder[];
  deliveryOrders: DeliveryOrder[];
  maintenanceReceiptOrders: MaintenanceReceiptOrder[];
  deviceReturnHistory: DeviceReturnHistory[];
  stocktakes: StocktakeV1[]; // سجل الأجهزة المرتجعة
  addDevice: (device: Omit<Device, 'id'> & { id?: string }) => void;
  updateDevice: (device: Device) => void;
  deleteDevice: (deviceId: string) => void;
  updateDeviceStatus: (deviceId: string, status: DeviceStatus) => void;
  getDevicesByStatus: (status: DeviceStatus) => Device[];
  addContact: (
    contact: Omit<Contact, 'id'>,
    type: 'client' | 'supplier',
  ) => void;
  updateContact: (contact: Contact, type: 'client' | 'supplier') => void;
  deleteContact: (contactId: number, type: 'client' | 'supplier') => void;
  checkClientRelations: (clientId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };
  checkSupplierRelations: (supplierId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };
  addWarehouse: (warehouse: Omit<Warehouse, 'id'>) => void;
  updateWarehouse: (warehouse: Warehouse) => void;
  deleteWarehouse: (warehouseId: number) => void;
  checkWarehouseRelations: (warehouseId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };
  addSale: (sale: Omit<Sale, 'id' | 'soNumber' | 'createdAt'>) => void;
  updateSale: (sale: Sale) => void;
  deleteSale: (saleId: number) => void;
  checkSaleRelations: (saleId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };
  addReturn: (returnVal: Omit<Return, 'id' | 'roNumber'>) => void;
  updateReturn: (returnVal: Return) => void;
  deleteReturn: (returnId: number) => void;
  // وظائف جديدة للتحقق من الأجهزة المرتجعة
  isDeviceReturned: (deviceId: string) => boolean;
  canDeviceBeReturned: (deviceId: string) => { canReturn: boolean; reason?: string };
  addDeviceReturnHistory: (history: Omit<DeviceReturnHistory, 'deviceId'> & { deviceId: string }) => void;
  addManufacturer: (manufacturer: Omit<Manufacturer, 'id'>) => Manufacturer;
  addDeviceModel: (model: Omit<DeviceModel, 'id'>) => void;
  addSupplyOrder: (order: Omit<SupplyOrder, 'id' | 'supplyOrderId' | 'createdAt'>) => void;
  updateSupplyOrder: (order: SupplyOrder) => void;
  deleteSupplyOrder: (orderId: number) => void;
  checkSupplyOrderRelations: (orderId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };
  addEvaluationOrder: (order: Omit<EvaluationOrder, 'id' | 'createdAt'>) => void;
  updateEvaluationOrder: (order: EvaluationOrder) => void;
  deleteEvaluationOrder: (orderId: number) => void;
  checkEvaluationOrderRelations: (orderId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };
  getDevicesByIds: (ids: string[]) => Device[];
  addMaintenanceLog: (log: Omit<MaintenanceLog, 'status'>) => void;
  acknowledgeMaintenanceLog: (deviceId: string) => void;
  revertDeviceToMaintenance: (deviceId: string) => void;
  addWarehouseTransfer: (
    order: Omit<WarehouseTransfer, 'id' | 'transferNumber'>,
  ) => void;
  updateWarehouseTransfer: (transfer: WarehouseTransfer) => void;
  deleteWarehouseTransfer: (transferId: number) => void;
  completeWarehouseTransfer: (transferId: number) => void;
  addUser: (user: Omit<User, 'id'>) => void;
  updateUser: (user: User) => void;
  deleteUser: (userId: number) => void;
  setCurrentUser: (user: User | null) => void;
  updateSystemSettings: (settings: SystemSettings) => void;
  addEmployeeRequest: (
    request: Omit<
      EmployeeRequest,
      | 'id'
      | 'requestNumber'
      | 'status'
      | 'requestDate'
      | 'employeeName'
      | 'employeeId'
    >,
  ) => void;
  processEmployeeRequest: (
    requestId: number,
    status: 'approved' | 'rejected',
    adminNotes?: string,
  ) => void;
  sendMessage: (
    message: Omit<
      InternalMessage,
      | 'id'
      | 'threadId'
      | 'senderId'
      | 'senderName'
      | 'sentDate'
      | 'status'
      | 'isRead'
    > & { recipientIds?: number[] },
  ) => void;
  updateMessage: (messageId: number, updates: Partial<InternalMessage>) => void;
  addAcceptanceOrder: (
    order: Omit<AcceptanceOrder, 'id' | 'acceptanceId'>,
  ) => void;
  updateAcceptanceOrder: (order: AcceptanceOrder) => void;
  deleteAcceptanceOrder: (orderId: number) => void;
  addMaintenanceOrder: (order: Omit<MaintenanceOrder, 'id'>) => void;
  updateMaintenanceOrder: (order: MaintenanceOrder) => void;
  deleteMaintenanceOrder: (orderId: number) => void;
  checkMaintenanceOrderRelations: (orderId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };
  checkDeviceRelationsInMaintenance: (deviceId: string, currentMaintenanceOrderId?: number) => { hasRelations: boolean; relatedOperations: string[] };
  addDeliveryOrder: (order: Omit<DeliveryOrder, 'id' | 'createdAt'>) => void;
  updateDeliveryOrder: (order: DeliveryOrder) => void;
  deleteDeliveryOrder: (orderId: number) => void;
  checkDeliveryOrderRelations: (orderId: number) => { canDelete: boolean; reason?: string; relatedOperations?: string[] };
  addMaintenanceReceiptOrder: (order: Omit<MaintenanceReceiptOrder, 'id'>) => void;
  updateMaintenanceReceiptOrder: (order: MaintenanceReceiptOrder) => void;
  deleteMaintenanceReceiptOrder: (orderId: number) => void;
  
  // وظائف الجرد
  addStocktake: (stocktake: Omit<StocktakeV1, 'id' | 'operationNumber' | 'createdAt' | 'lastModifiedAt'>) => void;
  updateStocktake: (stocktake: StocktakeV1) => void;
  deleteStocktake: (stocktakeId: number) => void;
  getFilteredStocktakes: (filter: StocktakeFilter) => StocktakeV1[];
  addStocktakeItem: (stocktakeId: number, item: StocktakeItemV1) => void;
  updateStocktakeItem: (stocktakeId: number, deviceId: string, updates: Partial<StocktakeItemV1>) => void;
  addStocktakeDiscrepancy: (stocktakeId: number, discrepancy: Omit<StocktakeDiscrepancy, 'resolved' | 'resolvedBy' | 'resolvedAt'>) => void;
  resolveStocktakeDiscrepancy: (stocktakeId: number, discrepancyIndex: number, resolutionNotes: string, resolvedBy: number) => void;
  changeStocktakeStatus: (stocktakeId: number, status: StocktakeStatus) => void;
  reviewStocktake: (stocktakeId: number, reviewedBy: number, reviewedByName: string, reviewNotes: string, approved: boolean) => void;
  
  // وظائف النسخ الاحتياطي والاستعادة
  createBackupSnapshot: () => any;
  restoreFromSnapshot: (snapshot: any) => void;
  exportStoreData: () => any;
  importStoreData: (data: any) => void;
};

const StoreContext = createContext<StoreContextType | undefined>(undefined);

export function StoreProvider({ children }: { children: ReactNode }) {
  const [devices, setDevices] = useState<Device[]>(initialDevices);
  const [clients, setClients] = useState<Contact[]>(initialClients);
  const [suppliers, setSuppliers] = useState<Contact[]>(initialSuppliers);
  const [warehouses, setWarehouses] = useState<Warehouse[]>(
    initialWarehouses || [],
  );
  const [sales, setSales] = useState<Sale[]>(initialSales);
  const [returns, setReturns] = useState<Return[]>(initialReturns);
  const [activities, setActivities] = useState<ActivityLog[]>([]);
  const [deviceReturnHistory, setDeviceReturnHistory] = useState<DeviceReturnHistory[]>([]);
  const [manufacturers, setManufacturers] =
    useState<Manufacturer[]>(initialManufacturers);
  const [deviceModels, setDeviceModels] =
    useState<DeviceModel[]>(initialDeviceModels);
  const [supplyOrders, setSupplyOrders] =
    useState<SupplyOrder[]>(initialSupplyOrders);

  // تحديث أوامر التوريد الموجودة لإضافة createdAt إذا لم تكن موجودة
  useEffect(() => {
    setSupplyOrders(prevOrders =>
      prevOrders.map(order => ({
        ...order,
        createdAt: order.createdAt || order.supplyDate + 'T00:00:00.000Z'
      }))
    );
  }, []);
  const [evaluationOrders, setEvaluationOrders] = useState<EvaluationOrder[]>(
    initialEvaluationOrders,
  );
  const [maintenanceHistory, setMaintenanceHistory] = useState<
    MaintenanceLog[]
  >([]);
  const [warehouseTransfers, setWarehouseTransfers] = useState<
    WarehouseTransfer[]
  >(initialWarehouseTransfers);
  const [users, setUsers] = useState<User[]>(initialUsers);
  const [currentUser, setCurrentUser] = useState<User | null>(initialUsers[0]);
  const [systemSettings, setSystemSettings] = useState<SystemSettings>(
    initialSystemSettings,
  );
  const [employeeRequests, setEmployeeRequests] = useState<EmployeeRequest[]>(
    initialEmployeeRequests,
  );
  const [internalMessages, setInternalMessages] = useState<InternalMessage[]>(
    initialInternalMessages,
  );
  const [acceptanceOrders, setAcceptanceOrders] = useState<AcceptanceOrder[]>(
    initialAcceptanceOrders,
  );
  const [maintenanceOrders, setMaintenanceOrders] = useState<
    MaintenanceOrder[]
  >(initialMaintenanceOrders);
  const [deliveryOrders, setDeliveryOrders] = useState<DeliveryOrder[]>(
    initialDeliveryOrders,
  );
  const [maintenanceReceiptOrders, setMaintenanceReceiptOrders] = useState<MaintenanceReceiptOrder[]>([]);
  const [stocktakes, setStocktakes] = useState<StocktakeV1[]>([]);

  // Load state from localStorage on initial render
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedData = localStorage.getItem('appData');
      if (savedData) {
        try {
          restoreFromSnapshot(JSON.parse(savedData));
          console.log('App data restored from localStorage.');
        } catch (error) {
          console.error('Failed to restore app data from localStorage:', error);
        }
      }
    }
  }, []);

  // تحديث البيانات القديمة لإضافة createdAt
  useEffect(() => {
    setMaintenanceOrders(prevOrders =>
      prevOrders.map(order => ({
        ...order,
        createdAt: order.createdAt || order.date + 'T00:00:00.000Z'
      }))
    );

    setMaintenanceReceiptOrders(prevOrders =>
      prevOrders.map(order => ({
        ...order,
        createdAt: order.createdAt || order.date + 'T00:00:00.000Z'
      }))
    );

    setDeliveryOrders(prevOrders =>
      prevOrders.map(order => ({
        ...order,
        createdAt: order.createdAt || order.date + 'T00:00:00.000Z'
      }))
    );

    setEvaluationOrders(prevOrders =>
      prevOrders.map(order => ({
        ...order,
        createdAt: order.createdAt || order.date + 'T00:00:00.000Z'
      }))
    );
  }, []);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const backup = createBackupSnapshot();
      localStorage.setItem('appData', JSON.stringify(backup));
    }
  }, [
    devices,
    clients,
    suppliers,
    warehouses,
    sales,
    returns,
    activities,
    deviceReturnHistory,
    manufacturers,
    deviceModels,
    supplyOrders,
    evaluationOrders,
    maintenanceHistory,
    warehouseTransfers,
    users,
    currentUser,
    systemSettings,
    employeeRequests,
    internalMessages,
    acceptanceOrders,
    maintenanceOrders,
    deliveryOrders,
    maintenanceReceiptOrders,
    stocktakes,
  ]);

  const addActivity = (log: Omit<ActivityLog, 'id' | 'date' | 'username'>) => {
    if (!currentUser) return;
    const newActivity: ActivityLog = {
      ...log,
      id: `act-${Date.now()}`,
      date: new Date(),
      username: currentUser.name,
    };
    setActivities((prev) => [newActivity, ...prev]);
  };

  const updateDeviceStatus = (deviceId: string, status: DeviceStatus) => {
    setDevices((prev) =>
      prev.map((d) => (d.id === deviceId ? { ...d, status } : d)),
    );
  };

  const getDevicesByStatus = (status: DeviceStatus) => {
    return devices.filter((d) => d.status === status);
  };

  // وظائف التحقق من الأجهزة المرتجعة
  const isDeviceReturned = (deviceId: string): boolean => {
    return deviceReturnHistory.some(
      history => history.deviceId === deviceId && history.status === 'returned'
    );
  };

  const canDeviceBeReturned = (deviceId: string): { canReturn: boolean; reason?: string } => {
    // التحقق من أن الجهاز موجود
    const device = devices.find(d => d.id === deviceId);
    if (!device) {
      return { canReturn: false, reason: 'الجهاز غير موجود في النظام' };
    }

    // التحقق من أن الجهاز ليس في المخزون حالياً
    const inventoryStatuses: DeviceStatus[] = [
      'متاح للبيع', 
      'بانتظار إرسال للصيانة', 
      'بانتظار استلام في الصيانة', 
      'قيد الإصلاح', 
      'بانتظار تسليم من الصيانة',
      'تالف',
      'معيب',
      'قيد النقل'
    ];
    
    if (inventoryStatuses.includes(device.status)) {
      return { canReturn: false, reason: 'الجهاز موجود حالياً في المخزون ولم يتم بيعه' };
    }

    // التحقق من أن الجهاز تم بيعه
    const isDeviceSold = sales.some(sale => 
      sale.items.some(item => item.deviceId === deviceId)
    );
    
    if (!isDeviceSold) {
      return { canReturn: false, reason: 'الجهاز لم يتم بيعه من قبل' };
    }

    // التحقق من أن الجهاز لم يتم إرجاعه سابقاً
    const isAlreadyReturned = isDeviceReturned(deviceId);
    if (isAlreadyReturned) {
      return { canReturn: false, reason: 'الجهاز تم إرجاعه سابقاً' };
    }

    return { canReturn: true };
  };

  const addDeviceReturnHistory = (history: Omit<DeviceReturnHistory, 'deviceId'> & { deviceId: string }) => {
    setDeviceReturnHistory(prev => [...prev, history]);
  };

  // Device CRUD
  const addDevice = (device: Omit<Device, 'id'> & { id?: string }) => {
    const newDevice = {
      ...device,
      id: device.id || `DEV-${Date.now()}`,
      dateAdded: new Date().toISOString(),
    } as Device;
    if (devices.some((d) => d.id === newDevice.id)) {
      console.error('Device with this ID already exists.');
      return;
    }
    setDevices((prev) => [...prev, newDevice]);
    addActivity({
      type: 'supply',
      description: `تم توريد جهاز جديد: ${newDevice.model}`,
    });
  };

      /**  حفظ أمر صيانة (مسوّدة أو فعلي) مع الحفاظ على تسلسل رقم الأمر وعدم تكراره */
      const addMaintenanceOrder = async (
        order: Omit<MaintenanceOrder, 'id' | 'createdAt'> & { id?: number; status?: 'wip' | 'completed' | 'draft' }
      ) => {
        /* 1. جمع كل المعرّفات الحالية */
        const allExisting = [...maintenanceOrders];

        /* 2. إذا جاء الأمر ومعه id (مثلاً عند تحويل مسوّدة) نستعمله كما هو */
        const useId =
          order.id && order.id > 0
            ? order.id
            : Math.max(0, ...allExisting.map((o) => o.id)) + 1;

        /* 3. منع تكرار رقم أمر موجود مسبقاً */
        const newOrderNumber = `MAINT-${useId}`;
        if (allExisting.some((o) => o.orderNumber === newOrderNumber)) {
          console.warn('Duplicate orderNumber blocked:', newOrderNumber);
          addActivity({
            type: 'maintenance',
            description: `⚠️ محاولة حفظ أمر مكرَّر رقم ${newOrderNumber} وتم حجبها.`,
          });
          return;
        }

        const newOrder: MaintenanceOrder = {
          ...order,
          id: useId,
          orderNumber: newOrderNumber,
          status: order.status || 'wip',
          createdAt: new Date().toISOString(), // ← تسجيل تلقائي للوقت
        };

        /* 4. إذا مسوّدة → حدّث أو أنشئ داخل Drafts */
        setMaintenanceOrders((prev) => [newOrder, ...prev]);

        /* 6. الأنشطة (يمكنك الإبقاء على خطوات API الخارجية كما كانت) */
        addActivity({
          type: 'maintenance',
          description: `تم إنشاء أمر صيانة ${newOrder.orderNumber}.`,
        });
      };


  const updateMaintenanceOrder = (updatedOrder: MaintenanceOrder) => {
    setMaintenanceOrders((prev) =>
      prev.map((o) => (o.id === updatedOrder.id ? {
        ...updatedOrder,
        createdAt: o.createdAt || new Date().toISOString() // ← الحفاظ على التاريخ الأصلي
      } : o)),
    );

    const originalOrder = maintenanceOrders.find(
      (o) => o.id === updatedOrder.id,
    );
    if (originalOrder) {
      const originalDeviceIds = new Set(originalOrder.items.map((i) => i.id));
      const updatedDeviceIds = new Set(updatedOrder.items.map((i) => i.id));

      const removedDeviceIds = [...originalDeviceIds].filter(
        (id) => !updatedDeviceIds.has(id),
      );
      setDevices((prev) =>
        prev.map((device) =>
          removedDeviceIds.includes(device.id)
            ? { ...device, status: 'تحتاج صيانة' }
            : device,
        ),
      );
    }

    updatedOrder.items.forEach((item) => {
      updateDeviceStatus(item.id, 'قيد الإصلاح');
    });

    addActivity({
      type: 'maintenance',
      description: `تم تحديث أمر الصيانة ${updatedOrder.orderNumber}`,
    });
  };

  // فحص العلاقات الشامل لأوامر الصيانة
  const checkMaintenanceOrderRelations = (orderId: number): {
    canDelete: boolean;
    reason?: string;
    relatedOperations?: string[]
  } => {
    const orderToDelete = maintenanceOrders.find((o) => o.id === orderId);
    if (!orderToDelete) return { canDelete: false, reason: 'أمر الصيانة غير موجود' };

    const deviceIdsInOrder = orderToDelete.items.map((item) => item.id);
    const relatedOperations: string[] = [];

    // فحص المبيعات
    const relatedSales = sales.filter(sale =>
      sale.items.some(item => deviceIdsInOrder.includes(item.deviceId))
    );
    if (relatedSales.length > 0) {
      relatedOperations.push(`${relatedSales.length} فاتورة مبيعات`);
    }

    // فحص المرتجعات
    const relatedReturns = returns.filter(returnOrder =>
      returnOrder.items.some(item => deviceIdsInOrder.includes(item.deviceId))
    );
    if (relatedReturns.length > 0) {
      relatedOperations.push(`${relatedReturns.length} أمر مرتجع`);
    }

    // فحص أوامر التوريد
    const relatedSupplyOrders = supplyOrders.filter(order =>
      order.items.some(item => deviceIdsInOrder.includes(item.imei))
    );
    if (relatedSupplyOrders.length > 0) {
      relatedOperations.push(`${relatedSupplyOrders.length} أمر توريد`);
    }

    // فحص أوامر التقييم
    const relatedEvaluationOrders = evaluationOrders.filter(order =>
      order.items.some(item => deviceIdsInOrder.includes(item.deviceId))
    );
    if (relatedEvaluationOrders.length > 0) {
      relatedOperations.push(`${relatedEvaluationOrders.length} أمر تقييم`);
    }

    // فحص التحويلات المخزنية
    const relatedWarehouseTransfers = warehouseTransfers.filter(transfer =>
      transfer.items.some(item => deviceIdsInOrder.includes(item.deviceId))
    );
    if (relatedWarehouseTransfers.length > 0) {
      relatedOperations.push(`${relatedWarehouseTransfers.length} تحويل مخزني`);
    }

    // فحص أوامر التسليم
    const relatedDeliveryOrders = deliveryOrders.filter(order =>
      order.items.some(item => deviceIdsInOrder.includes(item.deviceId))
    );
    if (relatedDeliveryOrders.length > 0) {
      relatedOperations.push(`${relatedDeliveryOrders.length} أمر تسليم`);
    }

    // فحص أوامر استلام الصيانة
    const relatedMaintenanceReceipts = maintenanceReceiptOrders.filter(order =>
      order.items.some(item => deviceIdsInOrder.includes(item.id))
    );
    if (relatedMaintenanceReceipts.length > 0) {
      relatedOperations.push(`${relatedMaintenanceReceipts.length} أمر استلام صيانة`);
    }

    // فحص سجلات الصيانة
    const relatedMaintenanceHistory = maintenanceHistory.filter(log =>
      deviceIdsInOrder.includes(log.deviceId)
    );
    if (relatedMaintenanceHistory.length > 0) {
      relatedOperations.push(`${relatedMaintenanceHistory.length} سجل صيانة`);
    }

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: 'يوجد عمليات مرتبطة بأجهزة هذا الأمر',
        relatedOperations
      };
    }

    return { canDelete: true };
  };

  const deleteMaintenanceOrder = (orderId: number) => {
    const relationCheck = checkMaintenanceOrderRelations(orderId);
    if (!relationCheck.canDelete) {
      throw new Error(`لا يمكن حذف أمر الصيانة: ${relationCheck.reason}${relationCheck.relatedOperations ? '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') : ''}`);
    }

    const orderToDelete = maintenanceOrders.find((o) => o.id === orderId);
    if (!orderToDelete) return;

    const deviceIdsToRevert = orderToDelete.items.map((item) => item.id);
    setDevices((prev) =>
      prev.map((device) =>
        deviceIdsToRevert.includes(device.id)
          ? { ...device, status: 'تحتاج صيانة' } // A reasonable default status
          : device,
      ),
    );

    setMaintenanceOrders((prev) => prev.filter((o) => o.id !== orderId));
    addActivity({
      type: 'maintenance',
      description: `تم حذف أمر الصيانة ${orderToDelete.orderNumber}`,
    });
  };

  const addDeliveryOrder = (order: Omit<DeliveryOrder, 'id' | 'createdAt'>) => {
    const maxId = deliveryOrders.reduce(
      (max, o) => (o.id > max ? o.id : max),
      0,
    );
    const newOrder: DeliveryOrder = {
      ...order,
      id: maxId + 1,
      createdAt: new Date().toISOString() // ← إضافة الختم الزمني
    };
    setDeliveryOrders((prev) => [newOrder, ...prev]);

    newOrder.items.forEach((item) => {
      let finalStatus: DeviceStatus;
      let logNote = item.notes;

      switch (item.result) {
        case 'Repaired':
          finalStatus = 'متاح للبيع'; // Change status to available for sale
          if (!logNote) logNote = 'تم الإصلاح بنجاح';
          // Update device's warehouseId and status
          setDevices(prev => prev.map(d => d.id === item.deviceId ? { ...d, warehouseId: newOrder.warehouseId, status: finalStatus } : d));
          break;
        case 'Unrepairable-Defective':
          finalStatus = 'معيب';
          if (!logNote) logNote = item.fault || 'لا يمكن إصلاحه - عيب فني';
          setDevices(prev => prev.map(d => d.id === item.deviceId ? { ...d, status: finalStatus } : d));
          break;
        case 'Unrepairable-Damaged':
          finalStatus = 'تالف';
          if (!logNote) logNote = item.damage || 'لا يمكن إصلاحه - تالف';
          setDevices(prev => prev.map(d => d.id === item.deviceId ? { ...d, status: finalStatus } : d));
          break;
        default:
          return;
      }

      // updateDeviceStatus(item.deviceId, finalStatus); // This is now handled within the setDevices map
      addMaintenanceLog({
        deviceId: item.deviceId,
        model: item.model,
        repairDate: new Date(newOrder.date).toISOString(),
        notes: logNote,
        result: item.result,
      });
    });

    addActivity({
      type: 'maintenance',
      description: `تم إنشاء أمر تسليم صيانة ${newOrder.deliveryOrderNumber}`,
    });
  };

  const updateDeliveryOrder = (updatedOrder: DeliveryOrder) => {
    setDeliveryOrders((prev) =>
      prev.map((o) => (o.id === updatedOrder.id ? updatedOrder : o)),
    );
    addActivity({
      type: 'maintenance',
      description: `تم تحديث أمر التسليم ${updatedOrder.deliveryOrderNumber}`,
    });
  };

  // فحص العلاقات الشامل لأوامر التسليم
  const checkDeliveryOrderRelations = (orderId: number): {
    canDelete: boolean;
    reason?: string;
    relatedOperations?: string[]
  } => {
    const orderToDelete = deliveryOrders.find((o) => o.id === orderId);
    if (!orderToDelete) return { canDelete: false, reason: 'أمر التسليم غير موجود' };

    const deviceIdsInOrder = orderToDelete.items.map((item) => item.deviceId);
    const relatedOperations: string[] = [];

    // فحص المبيعات - أي مبيعات تحتوي على أجهزة من هذا الأمر
    const relatedSales = sales.filter(sale =>
      sale.items.some(item => deviceIdsInOrder.includes(item.deviceId))
    );
    if (relatedSales.length > 0) {
      relatedOperations.push(`${relatedSales.length} فاتورة مبيعات`);
    }

    // فحص المرتجعات - أي مرتجعات تحتوي على أجهزة من هذا الأمر
    const relatedReturns = returns.filter(returnOrder =>
      returnOrder.items.some(item => deviceIdsInOrder.includes(item.deviceId))
    );
    if (relatedReturns.length > 0) {
      relatedOperations.push(`${relatedReturns.length} أمر مرتجع`);
    }

    // فحص التحويلات المخزنية
    const relatedTransfers = warehouseTransfers.filter(transfer =>
      transfer.items.some(item => deviceIdsInOrder.includes(item.deviceId))
    );
    if (relatedTransfers.length > 0) {
      relatedOperations.push(`${relatedTransfers.length} تحويل مخزني`);
    }

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: 'يوجد عمليات مرتبطة بأجهزة هذا الأمر',
        relatedOperations
      };
    }

    return { canDelete: true };
  };

  const deleteDeliveryOrder = (orderId: number) => {
    const relationCheck = checkDeliveryOrderRelations(orderId);
    if (!relationCheck.canDelete) {
      throw new Error(`لا يمكن حذف أمر التسليم: ${relationCheck.reason}${relationCheck.relatedOperations ? '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') : ''}`);
    }

    const orderToDelete = deliveryOrders.find((o) => o.id === orderId);
    if (!orderToDelete) return;

    // This action is complex. Reverting device statuses could lead to inconsistent states.
    // For now, we'll just delete the delivery order record.
    // A more robust implementation might require a log of the pre-delivery status.
    setDeliveryOrders((prev) => prev.filter((o) => o.id !== orderId));
    addActivity({
      type: 'maintenance',
      description: `تم حذف أمر التسليم ${orderToDelete.deliveryOrderNumber}`,
    });
  };

  // وظائف أوامر الاستلام من الصيانة
  const addMaintenanceReceiptOrder = (order: Omit<MaintenanceReceiptOrder, 'id' | 'createdAt'>) => {
    const maxId = maintenanceReceiptOrders.reduce(
      (max, o) => (o.id > max ? o.id : max),
      0,
    );
    const newOrder: MaintenanceReceiptOrder = {
      ...order,
      id: maxId + 1,
      createdAt: new Date().toISOString() // ← تسجيل تلقائي للوقت
    };
    setMaintenanceReceiptOrders((prev) => [newOrder, ...prev]);

    addActivity({
      type: 'maintenance',
      description: `تم إنشاء أمر استلام جديد ${newOrder.receiptNumber} يحتوي على ${newOrder.items.length} جهاز`,
    });
  };

  const updateMaintenanceReceiptOrder = (updatedOrder: MaintenanceReceiptOrder) => {
    setMaintenanceReceiptOrders((prev) =>
      prev.map((o) => (o.id === updatedOrder.id ? {
        ...updatedOrder,
        createdAt: o.createdAt || new Date().toISOString() // ← الحفاظ على التاريخ الأصلي
      } : o)),
    );
    addActivity({
      type: 'maintenance',
      description: `تم تحديث أمر الاستلام ${updatedOrder.receiptNumber}`,
    });
  };

  // فحص العلاقات الشامل للأجهزة الفردية في الصيانة
  const checkDeviceRelationsInMaintenance = (deviceId: string, currentMaintenanceOrderId?: number) => {
    const relatedOps: string[] = [];

    // فحص المبيعات - أي مبيعات تحتوي على هذا الجهاز
    const deviceInSales = sales.some(sale =>
      sale.items.some(item => item.deviceId === deviceId)
    );
    if (deviceInSales) relatedOps.push('مبيعات');

    // فحص المرتجعات - أي مرتجعات تحتوي على هذا الجهاز
    const deviceInReturns = returns.some(returnOrder =>
      returnOrder.items.some(item => item.deviceId === deviceId)
    );
    if (deviceInReturns) relatedOps.push('مرتجعات');

    // فحص أوامر التقييم - أي تقييم يحتوي على هذا الجهاز
    const deviceInEvaluations = evaluationOrders.some(evalOrder =>
      evalOrder.items.some(item => item.deviceId === deviceId)
    );
    if (deviceInEvaluations) relatedOps.push('تقييم');

    // فحص التحويلات المخزنية - أي تحويل يحتوي على هذا الجهاز
    const deviceInTransfers = warehouseTransfers.some(transfer =>
      transfer.items.some(item => item.deviceId === deviceId)
    );
    if (deviceInTransfers) relatedOps.push('تحويلات مخزنية');

    // فحص سجلات الصيانة - أي صيانة لهذا الجهاز
    const deviceInMaintenanceHistory = maintenanceHistory.some(maintenance =>
      maintenance.deviceId === deviceId
    );
    if (deviceInMaintenanceHistory) relatedOps.push('صيانة');

    // فحص أوامر الصيانة الأخرى - إذا كان الجهاز موجود في أوامر صيانة أخرى
    const deviceInOtherMaintenanceOrders = maintenanceOrders.some(order =>
      order.id !== currentMaintenanceOrderId &&
      order.items.some(item => item.id === deviceId)
    );
    if (deviceInOtherMaintenanceOrders) relatedOps.push('أوامر صيانة أخرى');

    // فحص أوامر التسليم - أي أمر تسليم يحتوي على هذا الجهاز
    const deviceInDeliveryOrders = deliveryOrders.some(order =>
      order.items.some(item => item.deviceId === deviceId)
    );
    if (deviceInDeliveryOrders) relatedOps.push('أوامر تسليم');

    // فحص أوامر استلام الصيانة - أي أمر استلام يحتوي على هذا الجهاز
    const deviceInMaintenanceReceipts = maintenanceReceiptOrders.some(order =>
      order.items.some(item => item.id === deviceId)
    );
    if (deviceInMaintenanceReceipts) relatedOps.push('أوامر استلام صيانة');

    // فحص أوامر التوريد - إذا كان الجهاز موجود في أوامر توريد
    const deviceInSupplyOrders = supplyOrders.some(order =>
      order.items.some(item => item.imei === deviceId)
    );
    if (deviceInSupplyOrders) relatedOps.push('أوامر توريد');

    // فحص عمليات الجرد - أي جرد يحتوي على هذا الجهاز
    const deviceInStocktakes = stocktakes.some(stocktake =>
      stocktake.items && stocktake.items.some(item => item.deviceId === deviceId)
    );
    if (deviceInStocktakes) relatedOps.push('عمليات جرد');

    return {
      hasRelations: relatedOps.length > 0,
      relatedOperations: relatedOps
    };
  };

  const deleteMaintenanceReceiptOrder = (orderId: number) => {
    const orderToDelete = maintenanceReceiptOrders.find((o) => o.id === orderId);
    if (!orderToDelete) return;

    setMaintenanceReceiptOrders((prev) => prev.filter((o) => o.id !== orderId));
    addActivity({
      type: 'maintenance',
      description: `تم حذف أمر الاستلام ${orderToDelete.receiptNumber}`,
    });
  };

  // وظائف الجرد
  const addStocktake = (stocktakeData: Omit<StocktakeV1, 'id' | 'operationNumber' | 'createdAt' | 'lastModifiedAt'>) => {
    const newId = Math.max(0, ...stocktakes.map(s => s.id)) + 1;
    const operationNumber = `ST-${Date.now()}`;
    const now = new Date().toISOString();
    
    const newStocktake: StocktakeV1 = {
      ...stocktakeData,
      id: newId,
      operationNumber,
      createdAt: now,
      lastModifiedAt: now,
      items: [], // Initialize items array
      discrepancies: [], // Initialize discrepancies array
      totalExpected: 0,
      totalScanned: 0,
      totalMatching: 0,
      totalMissing: 0,
      totalExtra: 0,
      totalDiscrepancies: 0,
      status: 'مسودة', // Default status
      createdBy: currentUser?.id || 0,
      createdByName: currentUser?.name || 'Unknown',
      warehouseName: warehouses.find(w => w.id === stocktakeData.warehouseId)?.name || 'Unknown',
      selectedModel: stocktakeData.selectedModel || 'all',
    };
    
    setStocktakes(prev => [newStocktake, ...prev]);
  };

  const updateStocktake = (stocktake: StocktakeV1) => {
    setStocktakes(prev =>
      prev.map(s =>
        s.id === stocktake.id
          ? { ...stocktake, lastModifiedAt: new Date().toISOString() }
          : s
      )
    );
  };

  const deleteStocktake = (stocktakeId: number) => {
    setStocktakes(prev => prev.filter(s => s.id !== stocktakeId));
  };

  const getFilteredStocktakes = (filter: StocktakeFilter): StocktakeV1[] => {
    return stocktakes.filter(stocktake => {
      if (filter.status && stocktake.status !== filter.status) return false;
      if (filter.warehouseId && stocktake.warehouseId !== filter.warehouseId) return false;
      if (filter.createdBy && stocktake.createdBy !== filter.createdBy) return false;
      if (filter.model && stocktake.selectedModel !== filter.model && filter.model !== 'all') return false;
      if (filter.dateFrom && stocktake.createdAt < filter.dateFrom) return false;
      if (filter.dateTo && stocktake.createdAt > filter.dateTo) return false;
      return true;
    });
  };

  const addStocktakeItem = (stocktakeId: number, item: StocktakeItemV1) => {
    setStocktakes(prev =>
      prev.map(s =>
        s.id === stocktakeId
          ? {
              ...s,
              items: [...s.items, item],
              lastModifiedAt: new Date().toISOString()
            }
          : s
      )
    );
  };

  const updateStocktakeItem = (stocktakeId: number, deviceId: string, updates: Partial<StocktakeItemV1>) => {
    setStocktakes(prev =>
      prev.map(s =>
        s.id === stocktakeId
          ? {
              ...s,
              items: s.items.map(item =>
                item.deviceId === deviceId
                  ? { ...item, ...updates }
                  : item
              ),
              lastModifiedAt: new Date().toISOString()
            }
          : s
      )
    );
  };

  const addStocktakeDiscrepancy = (stocktakeId: number, discrepancy: Omit<StocktakeDiscrepancy, 'resolved' | 'resolvedBy' | 'resolvedAt'>) => {
    setStocktakes(prev =>
      prev.map(s =>
        s.id === stocktakeId
          ? {
              ...s,
              discrepancies: [...s.discrepancies, { ...discrepancy, resolved: false }],
              lastModifiedAt: new Date().toISOString()
            }
          : s
      )
    );
  };

  const resolveStocktakeDiscrepancy = (stocktakeId: number, discrepancyIndex: number, resolutionNotes: string, resolvedBy: number) => {
    setStocktakes(prev =>
      prev.map(s =>
        s.id === stocktakeId
          ? {
              ...s,
              discrepancies: s.discrepancies.map((d, index) =>
                index === discrepancyIndex
                  ? {
                      ...d,
                      resolved: true,
                      resolutionNotes,
                      resolvedBy,
                      resolvedAt: new Date().toISOString()
                    }
                  : d
              ),
              lastModifiedAt: new Date().toISOString()
            }
          : s
      )
    );
  };

  const changeStocktakeStatus = (stocktakeId: number, status: StocktakeStatus) => {
    setStocktakes(prev =>
      prev.map(s =>
        s.id === stocktakeId
          ? {
              ...s,
              status,
              ...(status === 'جاري' && !s.startedAt ? { startedAt: new Date().toISOString() } : {}),
              ...(status === 'مكتمل' ? { completedAt: new Date().toISOString() } : {}),
              lastModifiedAt: new Date().toISOString()
            }
          : s
      )
    );
  };

  const reviewStocktake = (stocktakeId: number, reviewedBy: number, reviewedByName: string, reviewNotes: string, approved: boolean) => {
    setStocktakes(prev =>
      prev.map(s =>
        s.id === stocktakeId
          ? {
              ...s,
              reviewedBy,
              reviewedByName,
              reviewedAt: new Date().toISOString(),
              reviewNotes,
              approved,
              lastModifiedAt: new Date().toISOString()
            }
          : s
      )
    );
  };

  // Acceptance Orders
  const addAcceptanceOrder = (
    order: Omit<AcceptanceOrder, 'id' | 'acceptanceId'>,
  ) => {
    const maxId = acceptanceOrders.reduce(
      (max, o) => (o.id > max ? o.id : max),
      0,
    );
    const newId = maxId + 1;
    const newOrder: AcceptanceOrder = {
      ...order,
      id: newId,
      acceptanceId: `ACC-${newId}`,
      date: new Date().toISOString(),
    };
    setAcceptanceOrders((prev) =>
      [newOrder, ...prev].sort((a, b) => b.id - a.id),
    );

    newOrder.items.forEach((item) => {
      const newDevice: Device = {
        id: item.deviceId,
        model: item.model,
        status: 'متاح للبيع',
        storage: item.storage,
        price: item.price,
        condition: item.condition,
        warehouseId: newOrder.warehouseId,
        dateAdded: new Date().toISOString(),
      };
      setDevices((prev) => [...prev, newDevice]);
    });

    const warehouse = warehouses.find((w) => w.id === newOrder.warehouseId);
    addActivity({
      type: 'supply',
      description: `تم قبول ${newOrder.items.length} أجهزة في المخزن ${warehouse?.name || 'غير معروف'}`,
    });
  };

  const updateAcceptanceOrder = (updatedOrder: AcceptanceOrder) => {
    setAcceptanceOrders((prev) =>
      prev.map((o) => (o.id === updatedOrder.id ? updatedOrder : o)),
    );
    addActivity({
      type: 'supply',
      description: `تم تحديث أمر القبول ${updatedOrder.acceptanceId}`,
    });
  };

  const deleteAcceptanceOrder = (orderId: number) => {
    const orderToDelete = acceptanceOrders.find((o) => o.id === orderId);
    if (!orderToDelete) return;

    // Remove associated devices from inventory
    const deviceIdsToRemove = orderToDelete.items.map((item) => item.deviceId);
    setDevices((prevDevices) =>
      prevDevices.filter((device) => !deviceIdsToRemove.includes(device.id)),
    );

    setAcceptanceOrders((prev) => prev.filter((o) => o.id !== orderId));
    addActivity({
      type: 'supply',
      description: `تم حذف أمر القبول ${orderToDelete.acceptanceId}`,
    });
  };

  const updateDevice = (updatedDevice: Device) => {
    setDevices((prev) =>
      prev.map((d) => (d.id === updatedDevice.id ? updatedDevice : d)),
    );
  };

  const deleteDevice = (deviceId: string) => {
    setDevices((prev) => prev.filter((d) => d.id !== deviceId));
  };

  const getDevicesByIds = (ids: string[]) => {
    return devices.filter((d) => ids.includes(d.id));
  };

  // Contact CRUD
  const addContact = (
    contact: Omit<Contact, 'id'>,
    type: 'client' | 'supplier',
  ) => {
    const newContact = { ...contact, id: Date.now() };
    if (type === 'client') {
      setClients((prev) => [newContact, ...prev]);
    } else {
      setSuppliers((prev) => [newContact, ...prev]);
    }
  };

  const updateContact = (
    updatedContact: Contact,
    type: 'client' | 'supplier',
  ) => {
    if (type === 'client') {
      setClients((prev) =>
        prev.map((c) => (c.id === updatedContact.id ? updatedContact : c)),
      );
    } else {
      setSuppliers((prev) =>
        prev.map((s) => (s.id === updatedContact.id ? updatedContact : s)),
      );
    }
  };

  // فحص العلاقات الشامل للعملاء
  const checkClientRelations = (clientId: number): {
    canDelete: boolean;
    reason?: string;
    relatedOperations?: string[]
  } => {
    const clientToDelete = clients.find((c) => c.id === clientId);
    if (!clientToDelete) return { canDelete: false, reason: 'العميل غير موجود' };

    const relatedOperations: string[] = [];

    // فحص المبيعات - أي مبيعات لهذا العميل
    const relatedSales = sales.filter(sale => sale.clientName === clientToDelete.name);
    if (relatedSales.length > 0) {
      relatedOperations.push(`${relatedSales.length} فاتورة مبيعات`);
    }

    // فحص المرتجعات - أي مرتجعات من هذا العميل
    const relatedReturns = returns.filter(returnOrder => returnOrder.clientName === clientToDelete.name);
    if (relatedReturns.length > 0) {
      relatedOperations.push(`${relatedReturns.length} أمر مرتجع`);
    }

    // ملاحظة: أوامر التسليم لا ترتبط مباشرة بالعملاء في النظام الحالي

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: 'يوجد عمليات مرتبطة بهذا العميل',
        relatedOperations
      };
    }

    return { canDelete: true };
  };

  // فحص العلاقات الشامل للموردين
  const checkSupplierRelations = (supplierId: number): {
    canDelete: boolean;
    reason?: string;
    relatedOperations?: string[]
  } => {
    const supplierToDelete = suppliers.find((s) => s.id === supplierId);
    if (!supplierToDelete) return { canDelete: false, reason: 'المورد غير موجود' };

    const relatedOperations: string[] = [];

    // فحص أوامر التوريد - أي أوامر توريد من هذا المورد
    const relatedSupplyOrders = supplyOrders.filter(order => order.supplierId === supplierId);
    if (relatedSupplyOrders.length > 0) {
      relatedOperations.push(`${relatedSupplyOrders.length} أمر توريد`);
    }

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: 'يوجد عمليات مرتبطة بهذا المورد',
        relatedOperations
      };
    }

    return { canDelete: true };
  };

  const deleteContact = (contactId: number, type: 'client' | 'supplier') => {
    // فحص العلاقات قبل الحذف
    if (type === 'client') {
      const relationCheck = checkClientRelations(contactId);
      if (!relationCheck.canDelete) {
        throw new Error(`لا يمكن حذف العميل: ${relationCheck.reason}${relationCheck.relatedOperations ? '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') : ''}`);
      }
      setClients((prev) => prev.filter((c) => c.id !== contactId));
    } else {
      const relationCheck = checkSupplierRelations(contactId);
      if (!relationCheck.canDelete) {
        throw new Error(`لا يمكن حذف المورد: ${relationCheck.reason}${relationCheck.relatedOperations ? '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') : ''}`);
      }
      setSuppliers((prev) => prev.filter((s) => s.id !== contactId));
    }
  };

  // Warehouse CRUD
  const addWarehouse = (warehouse: Omit<Warehouse, 'id'>) => {
    const newWarehouse = { ...warehouse, id: Date.now() };
    setWarehouses((prev) => [newWarehouse, ...prev]);
  };

  const updateWarehouse = (updatedWarehouse: Warehouse) => {
    setWarehouses((prev) =>
      prev.map((w) => (w.id === updatedWarehouse.id ? updatedWarehouse : w)),
    );
  };

  // فحص العلاقات الشامل للمخازن
  const checkWarehouseRelations = (warehouseId: number): {
    canDelete: boolean;
    reason?: string;
    relatedOperations?: string[]
  } => {
    const warehouseToDelete = warehouses.find((w) => w.id === warehouseId);
    if (!warehouseToDelete) return { canDelete: false, reason: 'المخزن غير موجود' };

    const relatedOperations: string[] = [];

    // فحص المبيعات - أي مبيعات من هذا المخزن
    const relatedSales = sales.filter(sale => sale.warehouseName === warehouseToDelete.name);
    if (relatedSales.length > 0) {
      relatedOperations.push(`${relatedSales.length} فاتورة مبيعات`);
    }

    // فحص أوامر التوريد - أي أوامر توريد لهذا المخزن
    const relatedSupplyOrders = supplyOrders.filter(order => order.warehouseId === warehouseId);
    if (relatedSupplyOrders.length > 0) {
      relatedOperations.push(`${relatedSupplyOrders.length} أمر توريد`);
    }

    // فحص التحويلات المخزنية - أي تحويلات من أو إلى هذا المخزن
    const relatedTransfers = warehouseTransfers.filter(transfer =>
      transfer.fromWarehouseId === warehouseId || transfer.toWarehouseId === warehouseId
    );
    if (relatedTransfers.length > 0) {
      relatedOperations.push(`${relatedTransfers.length} تحويل مخزني`);
    }

    // فحص أوامر التسليم - أي أوامر تسليم من هذا المخزن
    const relatedDeliveries = deliveryOrders.filter(order => order.warehouseId === warehouseId);
    if (relatedDeliveries.length > 0) {
      relatedOperations.push(`${relatedDeliveries.length} أمر تسليم`);
    }

    // فحص الأجهزة - أي أجهزة موجودة في هذا المخزن
    const devicesInWarehouse = devices.filter(device => device.warehouseId === warehouseId);
    if (devicesInWarehouse.length > 0) {
      relatedOperations.push(`${devicesInWarehouse.length} جهاز`);
    }

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: 'يوجد عمليات أو أجهزة مرتبطة بهذا المخزن',
        relatedOperations
      };
    }

    return { canDelete: true };
  };

  const deleteWarehouse = (warehouseId: number) => {
    // فحص العلاقات قبل الحذف
    const relationCheck = checkWarehouseRelations(warehouseId);
    if (!relationCheck.canDelete) {
      throw new Error(`لا يمكن حذف المخزن: ${relationCheck.reason}${relationCheck.relatedOperations ? '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') : ''}`);
    }

    setWarehouses((prev) => prev.filter((w) => w.id !== warehouseId));
  };

  // Manufacturer & Model CRUD
  const addManufacturer = (
    manufacturer: Omit<Manufacturer, 'id'>,
  ): Manufacturer => {
    const newManufacturer = { ...manufacturer, id: Date.now() };
    setManufacturers((prev) => [newManufacturer, ...prev]);
    return newManufacturer;
  };

  const addDeviceModel = (model: Omit<DeviceModel, 'id'>) => {
    const newModel = { ...model, id: Date.now() };
    setDeviceModels((prev) => [newModel, ...prev]);
  };

  // Sales and Returns
  const addSale = (sale: Omit<Sale, 'id' | 'soNumber' | 'createdAt'>) => {
    const maxId = sales.reduce((max, s) => (s.id > max ? s.id : max), 0);
    const newId = maxId + 1;
    const newSale: Sale = {
      ...sale,
      id: newId,
      soNumber: `SO-${newId}`,
      createdAt: new Date().toISOString() // ← تسجيل تلقائي للوقت
    };
    setSales((prev) => [newSale, ...prev].sort((a, b) => b.id - a.id));

    newSale.items.forEach((item) => {
      updateDeviceStatus(item.deviceId, 'مباع');
    });
    addActivity({
      type: 'sale',
      description: `تم بيع ${newSale.items.length} أجهزة للعميل ${newSale.clientName}`,
    });
  };

  const updateSale = (updatedSale: Sale) => {
    const originalSale = sales.find((s) => s.id === updatedSale.id);
    if (!originalSale) return;

    // Devices removed from the sale should be set back to 'متاح للبيع'
    originalSale.items.forEach((item) => {
      if (
        !updatedSale.items.some((newItem) => newItem.deviceId === item.deviceId)
      ) {
        updateDeviceStatus(item.deviceId, 'متاح للبيع');
      }
    });

    // Devices added or kept in the sale should be set to 'مباع'
    updatedSale.items.forEach((item) => {
      updateDeviceStatus(item.deviceId, 'مباع');
    });

    setSales((prev) =>
      prev.map((s) => (s.id === updatedSale.id ? updatedSale : s)),
    );
    addActivity({
      type: 'sale',
      description: `تم تحديث الفاتورة ${updatedSale.soNumber}`,
    });
  };

  const deleteSale = (saleId: number) => {
    // التحقق من العلاقات أولاً
    const relationCheck = checkSaleRelations(saleId);
    if (!relationCheck.canDelete) {
      throw new Error(`لا يمكن حذف فاتورة المبيعات: ${relationCheck.reason}${relationCheck.relatedOperations ? '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') : ''}`);
    }

    const saleToDelete = sales.find((s) => s.id === saleId);
    if (!saleToDelete) return;

    // Revert devices to 'متاح للبيع' status
    saleToDelete.items.forEach((item) => {
      updateDeviceStatus(item.deviceId, 'متاح للبيع');
    });

    setSales((prev) => prev.filter((s) => s.id !== saleId));
    addActivity({
      type: 'sale',
      description: `تم حذف الفاتورة ${saleToDelete.soNumber}`,
    });
  };

  const addReturn = (returnVal: Omit<Return, 'id' | 'roNumber' | 'status'>) => {
    // التحقق من صحة جميع الأجهزة قبل إنشاء المرتجع
    for (const item of returnVal.items) {
      const validation = canDeviceBeReturned(item.deviceId);
      if (!validation.canReturn) {
        throw new Error(`لا يمكن إرجاع الجهاز ${item.deviceId}: ${validation.reason}`);
      }
      
      // التحقق من الجهاز البديل في حالة الاستبدال
      if (item.isReplacement && item.replacementDeviceId) {
        const replacementDevice = devices.find(d => d.id === item.replacementDeviceId);
        if (!replacementDevice) {
          throw new Error(`الجهاز البديل ${item.replacementDeviceId} غير موجود`);
        }
        if (replacementDevice.status !== 'متاح للبيع') {
          throw new Error(`الجهاز البديل ${item.replacementDeviceId} غير متاح للبيع`);
        }
      }
    }

    const maxId = returns.reduce((max, r) => (r.id > max ? r.id : max), 0);
    const newId = maxId + 1;
    const newReturn: Return = {
      ...returnVal,
      id: newId,
      roNumber: `RO-${newId}`,
      status: 'مقبول', // حالة افتراضية
      processedBy: currentUser?.name || 'غير محدد',
      processedDate: new Date().toISOString(),
    };
    setReturns((prev) => [newReturn, ...prev].sort((a, b) => b.id - a.id));

    const originalSale = sales.find((s) => s.id === newReturn.saleId);

    newReturn.items.forEach((item) => {
      // إضافة الجهاز للمخزون حسب سبب الإرجاع
      let newStatus: DeviceStatus;
      if (item.returnReason === 'رغبة العميل') {
        newStatus = 'متاح للبيع';
      } else if (item.returnReason === 'خلل مصنعي') {
        newStatus = 'بانتظار إرسال للصيانة';
      } else {
        newStatus = 'بانتظار إرسال للصيانة'; // افتراضي للأسباب الأخرى
      }
      
      updateDeviceStatus(item.deviceId, newStatus);

      // إضافة سجل للجهاز المرتجع
      addDeviceReturnHistory({
        deviceId: item.deviceId,
        returnOrderId: newReturn.id,
        returnDate: newReturn.date,
        returnReason: item.returnReason,
        isReplacement: item.isReplacement || false,
        replacementDeviceId: item.replacementDeviceId,
        originalDeviceId: item.originalDeviceId,
        status: 'returned'
      });

      // معالجة الاستبدال
      if (item.isReplacement && item.replacementDeviceId && originalSale) {
        const replacementDevice = devices.find(
          (d) => d.id === item.replacementDeviceId,
        );
        if (replacementDevice) {
          // تحديث معلومات الاستبدال للجهاز الأصلي
          setDevices((prev) =>
            prev.map((d) =>
              d.id === item.deviceId
                ? {
                    ...d,
                    replacementInfo: {
                      isReplacement: false,
                      replacedWithId: item.replacementDeviceId,
                      returnOrderId: newReturn.id,
                      replacementDate: new Date().toISOString(),
                      replacementReason: item.returnReason,
                    },
                  }
                : d,
            ),
          );

          // تحديث معلومات الاستبدال للجهاز البديل
          setDevices((prev) =>
            prev.map((d) =>
              d.id === item.replacementDeviceId
                ? {
                    ...d,
                    status: 'مباع',
                    replacementInfo: {
                      isReplacement: true,
                      originalDeviceId: item.deviceId,
                      returnOrderId: newReturn.id,
                      replacementDate: new Date().toISOString(),
                      replacementReason: item.returnReason,
                    },
                  }
                : d,
            ),
          );

          const newSaleItem: SaleItem = {
            deviceId: replacementDevice.id,
            model: replacementDevice.model,
            price: replacementDevice.price,
            condition: replacementDevice.condition,
          };

          const newSaleForReplacement: Omit<Sale, 'id' | 'soNumber'> = {
            opNumber: `OP-RET-${newReturn.id}`,
            date: new Date().toISOString(),
            clientName: newReturn.clientName,
            warehouseName: newReturn.warehouseName,
            items: [newSaleItem],
            notes: `استبدال للجهاز ${item.deviceId} من المرتجع رقم ${newReturn.roNumber}`,
            warrantyPeriod: originalSale.warrantyPeriod, // نفس مدة الضمان
          };

          addSale(newSaleForReplacement);
        }
      }
    });
    
    addActivity({
      type: 'return',
      description: `تم إنشاء أمر مرتجع ${newReturn.roNumber}`,
    });
  };

  const updateReturn = (updatedReturn: Return) => {
    // For simplicity, we assume devices can't be removed/added from a return order.
    // We just update the notes or other details.
    setReturns((prev) =>
      prev.map((r) => (r.id === updatedReturn.id ? updatedReturn : r)),
    );
    addActivity({
      type: 'return',
      description: `تم تحديث أمر المرتجع ${updatedReturn.roNumber}`,
    });
  };

  const deleteReturn = (returnId: number) => {
    const returnToDelete = returns.find((r) => r.id === returnId);
    if (!returnToDelete) return;

    // إرجاع حالات الأجهزة وتحديث سجل المرتجعات
    returnToDelete.items.forEach((item) => {
      updateDeviceStatus(item.deviceId, 'مباع');
      
      // تحديث سجل المرتجعات - تغيير الحالة إلى 'resold' بدلاً من حذف السجل
      setDeviceReturnHistory(prev => 
        prev.map(history => 
          history.deviceId === item.deviceId && history.returnOrderId === returnId
            ? { ...history, status: 'resold' }
            : history
        )
      );
      
      if (item.replacementDeviceId) {
        updateDeviceStatus(item.replacementDeviceId, 'متاح للبيع');
      }
    });

    setReturns((prev) => prev.filter((r) => r.id !== returnId));
    addActivity({
      type: 'return',
      description: `تم حذف أمر المرتجع ${returnToDelete.roNumber}`,
    });
  };

  // Supply Orders
  const addSupplyOrder = (order: Omit<SupplyOrder, 'id' | 'supplyOrderId' | 'createdAt'>) => {
    const maxId = supplyOrders.reduce((max, o) => (o.id > max ? o.id : max), 0);
    const newId = maxId + 1;
    const newOrder: SupplyOrder = {
      ...order,
      id: newId,
      supplyOrderId: `SUP-${newId}`,
      createdAt: new Date().toISOString(),
    };
    setSupplyOrders((prev) => [newOrder, ...prev].sort((a, b) => b.id - a.id));

    newOrder.items.forEach((item) => {
      const deviceExists = devices.some((d) => d.id === item.imei);
      if (!deviceExists) {
        const newDevice: Device = {
          id: item.imei,
          model: `${item.manufacturer} ${item.model}`,
          status: 'متاح للبيع', // Initially ready for evaluation
          storage: 'N/A', // Default value
          price: 0, // Default value
          condition: item.condition,
          warehouseId: newOrder.warehouseId,
          supplierId: newOrder.supplierId,
          dateAdded: new Date().toISOString(),
        };
        setDevices((prev) => [...prev, newDevice]);
      }
    });
    const supplier = suppliers.find((s) => s.id === newOrder.supplierId);
    addActivity({
      type: 'supply',
      description: `تم استلام ${newOrder.items.length} أجهزة من ${supplier?.name || 'مورد'}`,
    });
  };

  const updateSupplyOrder = (updatedOrder: SupplyOrder) => {
    const originalOrder = supplyOrders.find((o) => o.id === updatedOrder.id);
    if (!originalOrder) return;

    const originalImeis = new Set(originalOrder.items.map((item) => item.imei));
    const updatedImeis = new Set(updatedOrder.items.map((item) => item.imei));

    // IMEIs to be removed from inventory
    const imeisToRemove = originalOrder.items
      .filter((item) => !updatedImeis.has(item.imei))
      .map((item) => item.imei);

    // Items to be added to inventory
    const itemsToAdd = updatedOrder.items.filter(
      (item) => !originalImeis.has(item.imei),
    );

    // Remove devices from inventory
    if (imeisToRemove.length > 0) {
      setDevices((prevDevices) =>
        prevDevices.filter((device) => !imeisToRemove.includes(device.id)),
      );
    }

    // Add new devices to inventory
    if (itemsToAdd.length > 0) {
      const newDevices: Device[] = itemsToAdd.map((item) => ({
        id: item.imei,
        model: `${item.manufacturer} ${item.model}`,
        status: 'متاح للبيع',
        storage: 'N/A', // Default value
        price: 0, // Default value
        condition: item.condition,
        warehouseId: updatedOrder.warehouseId,
        supplierId: updatedOrder.supplierId,
        dateAdded: new Date().toISOString(),
      }));
      setDevices((prevDevices) => [...prevDevices, ...newDevices]);
    }

    // Update the order itself
    setSupplyOrders((prev) =>
      prev.map((o) => (o.id === updatedOrder.id ? updatedOrder : o)),
    );

    addActivity({
      type: 'supply',
      description: `تم تحديث أمر التوريد ${updatedOrder.supplyOrderId}`,
    });
  };

  // دالة للتحقق من العلاقات قبل حذف أمر التوريد
  const checkSupplyOrderRelations = (orderId: number): { canDelete: boolean; reason?: string; relatedOperations?: string[] } => {
    const orderToDelete = supplyOrders.find((o) => o.id === orderId);
    if (!orderToDelete) return { canDelete: false, reason: 'أمر التوريد غير موجود' };

    const imeisInOrder = orderToDelete.items.map((item) => item.imei);
    const relatedOperations: string[] = [];

    // فحص المبيعات - أي مبيعات تحتوي على أجهزة من هذا الأمر
    const relatedSales = sales.filter(sale =>
      sale.items.some(item => imeisInOrder.includes(item.deviceId))
    );
    if (relatedSales.length > 0) {
      relatedOperations.push(`${relatedSales.length} فاتورة مبيعات`);
    }

    // فحص المرتجعات - أي مرتجعات تحتوي على أجهزة من هذا الأمر
    const relatedReturns = returns.filter(returnOrder =>
      returnOrder.items.some(item => imeisInOrder.includes(item.deviceId))
    );
    if (relatedReturns.length > 0) {
      relatedOperations.push(`${relatedReturns.length} أمر مرتجع`);
    }

    // فحص أوامر التقييم - أي تقييم يحتوي على أجهزة من هذا الأمر
    const relatedEvaluations = evaluationOrders.filter(evalOrder =>
      evalOrder.items.some(item => imeisInOrder.includes(item.deviceId))
    );
    if (relatedEvaluations.length > 0) {
      relatedOperations.push(`${relatedEvaluations.length} أمر تقييم`);
    }

    // فحص التحويلات المخزنية - أي تحويل يحتوي على أجهزة من هذا الأمر
    const relatedTransfers = warehouseTransfers.filter(transfer =>
      transfer.items.some(item => imeisInOrder.includes(item.deviceId))
    );
    if (relatedTransfers.length > 0) {
      relatedOperations.push(`${relatedTransfers.length} تحويل مخزني`);
    }

    // فحص سجلات الصيانة - أي صيانة لأجهزة من هذا الأمر
    const relatedMaintenance = maintenanceHistory.filter(maintenance =>
      imeisInOrder.includes(maintenance.deviceId)
    );
    if (relatedMaintenance.length > 0) {
      relatedOperations.push(`${relatedMaintenance.length} سجل صيانة`);
    }

    // فحص أوامر التوريد الأخرى - إذا كانت الأجهزة موجودة في أوامر توريد أخرى
    const relatedSupplyOrders = supplyOrders.filter(order =>
      order.id !== orderId &&
      order.items.some(item => imeisInOrder.includes(item.imei))
    );
    if (relatedSupplyOrders.length > 0) {
      relatedOperations.push(`${relatedSupplyOrders.length} أمر توريد آخر`);
    }

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: 'يوجد عمليات مرتبطة بأجهزة هذا الأمر',
        relatedOperations
      };
    }

    return { canDelete: true };
  };

  // دالة للتحقق من العلاقات قبل حذف فاتورة المبيعات
  const checkSaleRelations = (saleId: number): { canDelete: boolean; reason?: string; relatedOperations?: string[] } => {
    const saleToDelete = sales.find((s) => s.id === saleId);
    if (!saleToDelete) return { canDelete: false, reason: 'فاتورة المبيعات غير موجودة' };

    const deviceIdsInSale = saleToDelete.items.map((item) => item.deviceId);
    const relatedOperations: string[] = [];

    // فحص المرتجعات - أي مرتجعات تحتوي على أجهزة من هذه الفاتورة
    const relatedReturns = returns.filter(returnOrder =>
      returnOrder.items.some(item => deviceIdsInSale.includes(item.deviceId))
    );
    if (relatedReturns.length > 0) {
      relatedOperations.push(`${relatedReturns.length} أمر مرتجع`);
    }

    // فحص سجلات الصيانة - أي صيانة لأجهزة من هذه الفاتورة
    const relatedMaintenance = maintenanceHistory.filter(maintenance =>
      deviceIdsInSale.includes(maintenance.deviceId)
    );
    if (relatedMaintenance.length > 0) {
      relatedOperations.push(`${relatedMaintenance.length} سجل صيانة`);
    }

    // فحص أوامر الصيانة - أي أمر صيانة يحتوي على أجهزة من هذه الفاتورة
    const relatedMaintenanceOrders = maintenanceOrders.filter(order =>
      order.items.some(item => deviceIdsInSale.includes(item.id))
    );
    if (relatedMaintenanceOrders.length > 0) {
      relatedOperations.push(`${relatedMaintenanceOrders.length} أمر صيانة`);
    }

    // فحص أوامر التسليم - أي أمر تسليم يحتوي على أجهزة من هذه الفاتورة
    const relatedDeliveryOrders = deliveryOrders.filter(order =>
      order.items.some(item => deviceIdsInSale.includes(item.deviceId))
    );
    if (relatedDeliveryOrders.length > 0) {
      relatedOperations.push(`${relatedDeliveryOrders.length} أمر تسليم`);
    }

    // فحص أوامر استلام الصيانة - أي أمر استلام يحتوي على أجهزة من هذه الفاتورة
    const relatedMaintenanceReceipts = maintenanceReceiptOrders.filter(order =>
      order.items.some(item => deviceIdsInSale.includes(item.id))
    );
    if (relatedMaintenanceReceipts.length > 0) {
      relatedOperations.push(`${relatedMaintenanceReceipts.length} أمر استلام صيانة`);
    }

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: 'يوجد عمليات مرتبطة بأجهزة هذه الفاتورة',
        relatedOperations
      };
    }

    return { canDelete: true };
  };

  const deleteSupplyOrder = (orderId: number) => {
    // التحقق من العلاقات أولاً
    const relationCheck = checkSupplyOrderRelations(orderId);
    if (!relationCheck.canDelete) {
      throw new Error(`لا يمكن حذف أمر التوريد: ${relationCheck.reason}${relationCheck.relatedOperations ? '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') : ''}`);
    }

    const orderToDelete = supplyOrders.find((o) => o.id === orderId);
    if (!orderToDelete) return;

    // Remove associated devices from inventory
    const imeisToRemove = orderToDelete.items.map((item) => item.imei);
    setDevices((prevDevices) =>
      prevDevices.filter((device) => !imeisToRemove.includes(device.id)),
    );

    // Remove the supply order
    setSupplyOrders((prevOrders) => prevOrders.filter((o) => o.id !== orderId));

    const supplier = suppliers.find((s) => s.id === orderToDelete.supplierId);
    addActivity({
      type: 'supply',
      description: `تم حذف أمر التوريد ${orderToDelete.supplyOrderId} و ${imeisToRemove.length} أجهزة من المورد ${supplier?.name || 'غير معروف'}`,
    });
  };

  // Evaluation Orders
  const addEvaluationOrder = (order: Omit<EvaluationOrder, 'id' | 'createdAt'>) => {
    const maxId = evaluationOrders.reduce(
      (max, o) => (o.id > max ? o.id : max),
      0,
    );
    const newOrder: EvaluationOrder = {
      ...order,
      id: maxId + 1,
      createdAt: new Date().toISOString() // ← إضافة الختم الزمني
    };
    setEvaluationOrders((prev) => [newOrder, ...prev]);

    // Update device status based on evaluation
    newOrder.items.forEach((item) => {
      let newStatus: DeviceStatus;
      switch (item.finalGrade) {
        case 'جاهز للبيع':
          newStatus = 'متاح للبيع';
          break;
        case 'يحتاج صيانة':
          newStatus = 'تحتاج صيانة';
          break;
        case 'عيب فني':
          newStatus = 'معيب';
          break;
        case 'تالف':
          newStatus = 'تالف';
          break;
        default:
          return;
      }
      updateDeviceStatus(item.deviceId, newStatus);
    });

    addActivity({
      type: 'evaluation',
      description: `تم تقييم ${newOrder.items.length} أجهزة في الأمر ${newOrder.orderId}`,
    });
  };

  const updateEvaluationOrder = (updatedOrder: EvaluationOrder) => {
    setEvaluationOrders((prev) =>
      prev.map((o) => (o.id === updatedOrder.id ? updatedOrder : o)),
    );

    updatedOrder.items.forEach((item) => {
      let newStatus: DeviceStatus;
      switch (item.finalGrade) {
        case 'جاهز للبيع':
          newStatus = 'متاح للبيع';
          break;
        case 'يحتاج صيانة':
          newStatus = 'تحتاج صيانة';
          break;
        case 'عيب فني':
          newStatus = 'معيب';
          break;
        case 'تالف':
          newStatus = 'تالف';
          break;
        default:
          return;
      }
      updateDeviceStatus(item.deviceId, newStatus);
    });

    addActivity({
      type: 'evaluation',
      description: `تم تحديث أمر التقييم ${updatedOrder.orderId}`,
    });
  };

  // فحص العلاقات الشامل لأوامر التقييم
  const checkEvaluationOrderRelations = (orderId: number): {
    canDelete: boolean;
    reason?: string;
    relatedOperations?: string[]
  } => {
    const orderToDelete = evaluationOrders.find((o) => o.id === orderId);
    if (!orderToDelete) return { canDelete: false, reason: 'أمر التقييم غير موجود' };

    const deviceIdsInOrder = orderToDelete.items.map((item) => item.deviceId);
    const relatedOperations: string[] = [];

    // فحص المبيعات - أي مبيعات تحتوي على أجهزة من هذا الأمر
    const relatedSales = sales.filter(sale =>
      sale.items.some(item => deviceIdsInOrder.includes(item.deviceId))
    );
    if (relatedSales.length > 0) {
      relatedOperations.push(`${relatedSales.length} فاتورة مبيعات`);
    }

    // فحص المرتجعات - أي مرتجعات تحتوي على أجهزة من هذا الأمر
    const relatedReturns = returns.filter(returnOrder =>
      returnOrder.items.some(item => deviceIdsInOrder.includes(item.deviceId))
    );
    if (relatedReturns.length > 0) {
      relatedOperations.push(`${relatedReturns.length} أمر مرتجع`);
    }

    // فحص التحويلات المخزنية
    const relatedTransfers = warehouseTransfers.filter(transfer =>
      transfer.items.some(item => deviceIdsInOrder.includes(item.deviceId))
    );
    if (relatedTransfers.length > 0) {
      relatedOperations.push(`${relatedTransfers.length} تحويل مخزني`);
    }

    // فحص أوامر الصيانة
    const relatedMaintenanceOrders = maintenanceOrders.filter(order =>
      order.items.some(item => deviceIdsInOrder.includes(item.id))
    );
    if (relatedMaintenanceOrders.length > 0) {
      relatedOperations.push(`${relatedMaintenanceOrders.length} أمر صيانة`);
    }

    if (relatedOperations.length > 0) {
      return {
        canDelete: false,
        reason: 'يوجد عمليات مرتبطة بأجهزة هذا الأمر',
        relatedOperations
      };
    }

    return { canDelete: true };
  };

  const deleteEvaluationOrder = (orderId: number) => {
    const relationCheck = checkEvaluationOrderRelations(orderId);
    if (!relationCheck.canDelete) {
      throw new Error(`لا يمكن حذف أمر التقييم: ${relationCheck.reason}${relationCheck.relatedOperations ? '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') : ''}`);
    }

    const orderToDelete = evaluationOrders.find((o) => o.id === orderId);
    if (!orderToDelete) return;

    // Note: Deleting an evaluation order does not automatically revert device status,
    // as their original state could be varied. Users should re-evaluate if necessary.
    setEvaluationOrders((prev) => prev.filter((o) => o.id !== orderId));
    addActivity({
      type: 'evaluation',
      description: `تم حذف أمر التقييم ${orderToDelete.orderId}`,
    });
  };

  // Maintenance Log
  const addMaintenanceLog = (log: Omit<MaintenanceLog, 'status'>) => {
    const newLog: MaintenanceLog = { ...log, status: 'pending' };
    setMaintenanceHistory((prev) => [newLog, ...prev]);
  };

  const acknowledgeMaintenanceLog = (deviceId: string) => {
    setMaintenanceHistory((prev) => {
      const historyCopy = [...prev];
      const logIndex = historyCopy.findIndex(
        (log) => log.deviceId === deviceId && log.status === 'pending',
      );
      if (logIndex !== -1) {
        const device = devices.find((d) => d.id === deviceId);
        const warehouse = warehouses.find((w) => w.id === device?.warehouseId);
        historyCopy[logIndex] = {
          ...historyCopy[logIndex],
          status: 'acknowledged',
          acknowledgedDate: new Date().toISOString(),
          warehouseName: warehouse?.name || 'غير معروف',
          acknowledgedBy: 'مدير النظام',
        };
      }
      return historyCopy;
    });
  };

  const revertDeviceToMaintenance = (deviceId: string) => {
    const device = devices.find((d) => d.id === deviceId);
    if (!device) return;

    // 1. Change device status back to "قيد الإصلاح"
    updateDeviceStatus(deviceId, 'قيد الإصلاح');

    // 2. Remove the corresponding "pending" log from maintenanceHistory
    setMaintenanceHistory((prev) =>
      prev.filter(
        (log) => !(log.deviceId === deviceId && log.status === 'pending'),
      ),
    );

    addActivity({
      type: 'maintenance',
      description: `تمت استعادة الجهاز ${device.model} إلى الصيانة`,
    });
  };

  // Warehouse Transfers
  const addWarehouseTransfer = (
    order: Omit<WarehouseTransfer, 'id' | 'transferNumber'>,
  ) => {
    const maxId = warehouseTransfers.reduce(
      (max, t) => (t.id > max ? t.id : max),
      0,
    );
    const newId = maxId + 1;
    const newTransfer: WarehouseTransfer = {
      ...order,
      id: newId,
      transferNumber: `WTO-${newId}`,
    };
    setWarehouseTransfers((prev) => [newTransfer, ...prev]);

    if (order.status === 'completed') {
      newTransfer.items.forEach((item) => {
        updateDeviceStatus(item.deviceId, 'متاح للبيع');
        const device = devices.find(d => d.id === item.deviceId);
        if (device) {
            updateDevice({...device, warehouseId: newTransfer.toWarehouseId});
        }
      });
    } else {
        newTransfer.items.forEach((item) => {
            updateDeviceStatus(item.deviceId, 'قيد النقل');
        });
    }
    
    addActivity({
      type: 'transfer',
      description: `تم إنشاء أمر تحويل ${newTransfer.transferNumber} من ${order.fromWarehouseName} إلى ${order.toWarehouseName}`,
    });
  };
  
  const updateWarehouseTransfer = (updatedTransfer: WarehouseTransfer) => {
      setWarehouseTransfers(prev => prev.map(t => t.id === updatedTransfer.id ? updatedTransfer : t));
      addActivity({
          type: 'transfer',
          description: `تم تحديث أمر التحويل ${updatedTransfer.transferNumber}`
      });
  };

  const deleteWarehouseTransfer = (transferId: number) => {
      const transfer = warehouseTransfers.find(t => t.id === transferId);
      if(!transfer) return;
      if(transfer.status === 'pending'){
          transfer.items.forEach(item => {
              updateDeviceStatus(item.deviceId, 'متاح للبيع');
          });
      }
      setWarehouseTransfers(prev => prev.filter(t => t.id !== transferId));
      addActivity({
          type: 'transfer',
          description: `تم حذف أمر التحويل ${transfer.transferNumber}`
      });
  };

  const completeWarehouseTransfer = (transferId: number) => {
    const transfer = warehouseTransfers.find((t) => t.id === transferId);
    if (!transfer) return;

    setWarehouseTransfers((prev) =>
      prev.map((t) =>
        t.id === transferId ? { ...t, status: 'completed' } : t,
      ),
    );

    setDevices((prevDevices) =>
      prevDevices.map((device) => {
        if (transfer.items.some((item) => item.deviceId === device.id)) {
          return {
            ...device,
            status: 'متاح للبيع',
            warehouseId: transfer.toWarehouseId,
          };
        }
        return device;
      }),
    );

    addActivity({
      type: 'transfer',
      description: `تم استلام أمر التحويل ${transfer.transferNumber} في مخزن ${transfer.toWarehouseName}`,
    });
  };

  // User CRUD
  const addUser = (user: Omit<User, 'id'>) => {
    const newUser = { ...user, id: Date.now() };
    setUsers((prev) => [newUser, ...prev]);
  };

  const updateUser = (updatedUser: User) => {
    setUsers((prev) =>
      prev.map((u) => (u.id === updatedUser.id ? updatedUser : u)),
    );
  };

  const deleteUser = (userId: number) => {
    setUsers((prev) => prev.filter((u) => u.id !== userId));
  };

  const setCurrentUserFunc = (user: User | null) => {
    setCurrentUser(user);
  };

  const updateSystemSettings = (settings: SystemSettings) => {
    setSystemSettings(settings);
  };

  // Employee Requests
  const addEmployeeRequest = (
    request: Omit<
      EmployeeRequest,
      | 'id'
      | 'requestNumber'
      | 'status'
      | 'requestDate'
      | 'employeeName'
      | 'employeeId'
    >,
  ) => {
    const maxId = employeeRequests.reduce(
      (max, r) => (r.id > max ? r.id : max),
      0,
    );
    const newId = maxId + 1;
    const employeeName = currentUser?.name || 'مستخدم غير معروف';
    const employeeId = currentUser?.id || 0;
    const newRequest: EmployeeRequest = {
      ...request,
      id: newId,
      employeeName: employeeName,
      employeeId: employeeId,
      requestNumber: `REQ-${newId}`,
      status: 'قيد المراجعة',
      requestDate: new Date().toISOString(),
    };
    setEmployeeRequests((prev) => [newRequest, ...prev]);
    addActivity({
      type: 'request',
      description: `تم إرسال طلب جديد ${newRequest.requestNumber}`,
    });
  };

  const processEmployeeRequest = (
    requestId: number,
    status: 'approved' | 'rejected',
    adminNotes: string = '',
  ) => {
    const request = employeeRequests.find((r) => r.id === requestId);
    if (!request) return;

    const newStatus = status === 'approved' ? 'تم التنفيذ' : 'مرفوض';

    // Update the request itself
    setEmployeeRequests((prev) =>
      prev.map((req) =>
        req.id === requestId
          ? {
              ...req,
              status: newStatus,
              adminNotes,
              resolutionDate: new Date().toISOString(),
            }
          : req,
      ),
    );

    addActivity({
      type: 'request',
      description: `تم ${newStatus === 'تم التنفيذ' ? 'تنفيذ' : 'رفض'} الطلب ${request.requestNumber}`,
    });

    // Send a notification message back to the employee
    const employee = users.find(u => u.id === request.employeeId);
    sendMessage({
      recipientId: request.employeeId,
      recipientName: employee?.name || 'غير معروف',
      text: `تم ${newStatus === 'تم التنفيذ' ? 'قبول' : 'رفض'} طلبك رقم ${request.requestNumber}.
 ملاحظات الإدارة: ${adminNotes || 'لا يوجد'}`,
      employeeRequestId: request.id,
    });
  };

  // Internal Messaging
  const sendMessage = (
    message: Omit<
      InternalMessage,
      | 'id'
      | 'threadId'
      | 'senderId'
      | 'senderName'
      | 'sentDate'
      | 'status'
      | 'isRead'
    > & { recipientIds?: number[] },
  ) => {
    if (!currentUser) return;
  
    const { recipientIds, ...messageData } = message;
    const newId = (internalMessages.length > 0 ? Math.max(...internalMessages.map((m) => m.id)) : 0) + 1;
  
    // Determine the threadId
    let threadId: number;
  
    if (message.parentMessageId) {
      // It's a reply, use the parent's threadId
      const parentMessage = internalMessages.find(m => m.id === message.parentMessageId);
      threadId = parentMessage?.threadId || newId; // Fallback to newId if parent not found
    } else if (recipientIds && recipientIds.length > 0) {
      // It's a new group message
      threadId = newId;
    } else {
      // It's a new direct message
      const existingThread = internalMessages.find(m =>
        (m.senderId === currentUser.id && m.recipientId === message.recipientId) ||
        (m.senderId === message.recipientId && m.recipientId === currentUser.id) &&
        (!m.recipientIds || m.recipientIds.length === 0)
      );
      threadId = existingThread?.threadId || newId;
    }
  
    if (recipientIds && recipientIds.length > 0) {
      // Group message logic
      const newMessages: InternalMessage[] = recipientIds.map((recipientId, index) => {
        const recipient = users.find((u) => u.id === recipientId);
        return {
          id: newId + index,
          threadId: threadId,
          senderId: currentUser.id,
          senderName: currentUser.fullName || currentUser.name,
          sentDate: new Date().toISOString(),
          status: 'مرسلة',
          isRead: false,
          ...messageData,
          recipientId: recipientId,
          recipientName: recipient?.fullName || recipient?.name || '',
          recipientIds: recipientIds.filter(id => id !== recipientId), // Store other recipients
        };
      });
      setInternalMessages((prev) => [...newMessages, ...prev]);
    } else {
      // Single message logic
      const recipient = users.find((u) => u.id === message.recipientId);
      const newMessage: InternalMessage = {
        id: newId,
        threadId: threadId,
        senderId: currentUser.id,
        senderName: currentUser.fullName || currentUser.name,
        sentDate: new Date().toISOString(),
        status: 'مرسلة',
        isRead: false,
        ...messageData,
        recipientName: message.recipientName || recipient?.fullName || recipient?.name || 'الكل',
      };
      setInternalMessages((prev) => [newMessage, ...prev]);
    }
  };

  const updateMessage = (
    messageId: number,
    updates: Partial<InternalMessage>,
  ) => {
    setInternalMessages((prev) =>
      prev.map((m) => (m.id === messageId ? { ...m, ...updates } : m)),
    );
  };

  const value = {
    devices,
    clients,
    suppliers,
    warehouses,
    sales,
    returns,
    activities,
    manufacturers,
    deviceModels,
    supplyOrders,
    evaluationOrders,
    maintenanceHistory,
    warehouseTransfers,
    users,
    currentUser,
    systemSettings,
    employeeRequests,
    internalMessages,
    acceptanceOrders,
    maintenanceOrders,
    deliveryOrders,
    maintenanceReceiptOrders,
    deviceReturnHistory,
    stocktakes,
    addDevice,
    updateDevice,
    deleteDevice,
    updateDeviceStatus,
    getDevicesByStatus,
    getDevicesByIds,
    addContact,
    updateContact,
    deleteContact,
    checkClientRelations,
    checkSupplierRelations,
    addWarehouse,
    updateWarehouse,
    deleteWarehouse,
    checkWarehouseRelations,
    addSale,
    updateSale,
    deleteSale,
    checkSaleRelations,
    addReturn,
    updateReturn,
    deleteReturn,
    isDeviceReturned,
    canDeviceBeReturned,
    addDeviceReturnHistory,
    addManufacturer,
    addDeviceModel,
    addSupplyOrder,
    updateSupplyOrder,
    deleteSupplyOrder,
    checkSupplyOrderRelations,
    addEvaluationOrder,
    updateEvaluationOrder,
    deleteEvaluationOrder,
    checkEvaluationOrderRelations,
    addMaintenanceLog,
    acknowledgeMaintenanceLog,
    revertDeviceToMaintenance,
    addWarehouseTransfer,
    updateWarehouseTransfer,
    deleteWarehouseTransfer,
    completeWarehouseTransfer,
    addUser,
    updateUser,
    deleteUser,
    setCurrentUser: setCurrentUserFunc,
    updateSystemSettings,
    addEmployeeRequest,
    processEmployeeRequest,
    sendMessage,
    updateMessage,
    addAcceptanceOrder,
    updateAcceptanceOrder,
    deleteAcceptanceOrder,
    addMaintenanceOrder,
    updateMaintenanceOrder,
    deleteMaintenanceOrder,
    checkMaintenanceOrderRelations,
    checkDeviceRelationsInMaintenance,
    addDeliveryOrder,
    updateDeliveryOrder,
    deleteDeliveryOrder,
    checkDeliveryOrderRelations,
    addMaintenanceReceiptOrder,
    updateMaintenanceReceiptOrder,
    deleteMaintenanceReceiptOrder,
    addStocktake,
    updateStocktake,
    deleteStocktake,
    getFilteredStocktakes,
    addStocktakeItem,
    updateStocktakeItem,
    addStocktakeDiscrepancy,
    resolveStocktakeDiscrepancy,
    changeStocktakeStatus,
    reviewStocktake,
    // Backup and restore functions
    createBackupSnapshot,
    restoreFromSnapshot,
    exportStoreData,
    importStoreData,
  };

  // Backup and restore functions
  function createBackupSnapshot() {
    return {
      timestamp: new Date().toISOString(),
      data: {
        devices,
        clients,
        suppliers,
        warehouses,
        sales,
        returns,
        manufacturers,
        deviceModels,
        supplyOrders,
        evaluationOrders,
        maintenanceOrders,
        maintenanceHistory,
        warehouseTransfers,
        deliveryOrders,
        employeeRequests,
        internalMessages,
        stocktakes,
        systemSettings,
        currentUser,
        users, // Ensure users are included in the backup
        activities,
        deviceReturnHistory,
        acceptanceOrders,
      },
    };
  }

  function restoreFromSnapshot(snapshot: any) {
    if (!snapshot || typeof snapshot !== 'object') {
      console.error('Invalid backup snapshot - not an object');
      return;
    }

    let data = snapshot;

    // Handle wrapped data format
    if (snapshot.data && typeof snapshot.data === 'object') {
      data = snapshot.data;
    }

    try {
      // Restore all state with fallbacks for missing data
      if (data.devices !== undefined)
        setDevices(Array.isArray(data.devices) ? data.devices : []);
      if (data.clients !== undefined)
        setClients(Array.isArray(data.clients) ? data.clients : []);
      if (data.suppliers !== undefined)
        setSuppliers(Array.isArray(data.suppliers) ? data.suppliers : []);
      if (data.warehouses !== undefined)
        setWarehouses(Array.isArray(data.warehouses) ? data.warehouses : []);
      if (data.sales !== undefined)
        setSales(Array.isArray(data.sales) ? data.sales : []);
      if (data.returns !== undefined)
        setReturns(Array.isArray(data.returns) ? data.returns : []);
      if (data.manufacturers !== undefined)
        setManufacturers(
          Array.isArray(data.manufacturers) ? data.manufacturers : [],
        );
      if (data.deviceModels !== undefined)
        setDeviceModels(
          Array.isArray(data.deviceModels) ? data.deviceModels : [],
        );
      if (data.supplyOrders !== undefined)
        setSupplyOrders(
          Array.isArray(data.supplyOrders) ? data.supplyOrders : [],
        );
      if (data.evaluationOrders !== undefined)
        setEvaluationOrders(
          Array.isArray(data.evaluationOrders) ? data.evaluationOrders : [],
        );
      if (data.maintenanceOrders !== undefined)
        setMaintenanceOrders(
          Array.isArray(data.maintenanceOrders) ? data.maintenanceOrders : [],
        );
      if (data.maintenanceHistory !== undefined)
        setMaintenanceHistory(
          Array.isArray(data.maintenanceHistory)
            ? data.maintenanceHistory
            : [],
        );
      if (data.warehouseTransfers !== undefined)
        setWarehouseTransfers(
          Array.isArray(data.warehouseTransfers)
            ? data.warehouseTransfers
            : [],
        );
      if (data.deliveryOrders !== undefined)
        setDeliveryOrders(
          Array.isArray(data.deliveryOrders) ? data.deliveryOrders : [],
        );
      if (data.employeeRequests !== undefined)
        setEmployeeRequests(
          Array.isArray(data.employeeRequests) ? data.employeeRequests : [],
        );
      if (data.internalMessages !== undefined)
        setInternalMessages(
          Array.isArray(data.internalMessages) ? data.internalMessages : [],
        );
      if (data.stocktakes !== undefined)
        setStocktakes(Array.isArray(data.stocktakes) ? data.stocktakes : []);
      if (data.users !== undefined)
        setUsers(Array.isArray(data.users) ? data.users : []);
      if (data.activities !== undefined)
        setActivities(Array.isArray(data.activities) ? data.activities : []);
      if (data.deviceReturnHistory !== undefined)
        setDeviceReturnHistory(
          Array.isArray(data.deviceReturnHistory)
            ? data.deviceReturnHistory
            : [],
        );
      if (data.acceptanceOrders !== undefined)
        setAcceptanceOrders(
          Array.isArray(data.acceptanceOrders) ? data.acceptanceOrders : [],
        );

      // Restore settings and user if available
      if (data.systemSettings && typeof data.systemSettings === 'object') {
        setSystemSettings(data.systemSettings);
      }

      if (data.currentUser && typeof data.currentUser === 'object') {
        setCurrentUser(data.currentUser);
      }

      console.log('Successfully restored data from backup');
    } catch (error) {
      console.error('Error during restore:', error);
      throw new Error(
        'Failed to restore backup data: ' +
          (error instanceof Error ? error.message : 'Unknown error'),
      );
    }
  }

  function exportStoreData() {
    return createBackupSnapshot();
  }

  function importStoreData(data: any) {
    restoreFromSnapshot(data);
  }

  // تحديث البيانات القديمة لتتضمن الختم الزمني
  useEffect(() => {
    setSales(prevSales =>
      prevSales.map(sale => ({
        ...sale,
        createdAt: sale.createdAt || sale.date + 'T00:00:00.000Z',
        employeeName: sale.employeeName || 'مدير النظام'
      }))
    );
  }, []);

  return (
    <StoreContext.Provider value={value}>{children}</StoreContext.Provider>
  );
}

export function useStore() {
  const context = useContext(StoreContext);
  if (context === undefined) {
    throw new Error('useStore must be used within a StoreProvider');
  }
  return context;
}
